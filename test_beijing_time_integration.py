#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
北京时间集成测试脚本
验证所有系统模块的时间处理都已正确转换为北京时间
"""

import sys
import os
import json
from datetime import datetime, timezone, timedelta

# 添加项目根目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from beijing_time import beijing_now, beijing_now_iso, format_beijing_time, BEIJING_TZ
from points_system import PointsSystem
from auth import UserManager
from redemption_system import RedemptionSystem

def test_beijing_time_module():
    """测试北京时间工具模块"""
    print("=== 测试北京时间工具模块 ===")
    
    # 测试基本功能
    beijing_time = beijing_now()
    print(f"当前北京时间: {beijing_time}")
    print(f"时区信息: {beijing_time.tzinfo}")
    
    # 验证时区是否正确 (UTC+8)
    expected_offset = timedelta(hours=8)
    actual_offset = beijing_time.utcoffset()
    assert actual_offset == expected_offset, f"时区偏移不正确: 期望 {expected_offset}, 实际 {actual_offset}"
    
    # 测试ISO格式
    iso_time = beijing_now_iso()
    print(f"ISO格式: {iso_time}")
    assert "+08:00" in iso_time, "ISO格式应包含北京时区信息"
    
    # 测试格式化
    formatted = format_beijing_time()
    print(f"格式化时间: {formatted}")
    
    print("✓ 北京时间工具模块测试通过\n")

def test_points_system_time():
    """测试积分系统的时间处理"""
    print("=== 测试积分系统时间处理 ===")
    
    # 创建测试积分系统
    points_system = PointsSystem('test_points_data.json')
    
    # 添加一个测试交易
    success = points_system.add_transaction(
        username='test_user',
        points_change=10,
        transaction_type='test',
        description='测试北京时间'
    )
    
    assert success, "添加交易失败"
    
    # 获取交易记录
    transactions = points_system.get_user_transactions('test_user', 1)
    assert len(transactions) > 0, "没有找到交易记录"
    
    # 验证时间戳格式
    timestamp = transactions[0]['timestamp']
    print(f"交易时间戳: {timestamp}")
    
    # 验证时间戳包含北京时区信息
    assert "+08:00" in timestamp, f"时间戳应包含北京时区信息: {timestamp}"
    
    # 清理测试文件
    if os.path.exists('test_points_data.json'):
        os.remove('test_points_data.json')
    
    print("✓ 积分系统时间处理测试通过\n")

def test_auth_system_time():
    """测试用户管理系统的时间处理"""
    print("=== 测试用户管理系统时间处理 ===")
    
    # 创建测试用户管理系统
    user_manager = UserManager('test_users.json')
    
    # 注册测试用户
    success, message = user_manager.register_user(
        username='test_beijing_user',
        password='test123456',
        email='<EMAIL>'
    )
    
    assert success, f"用户注册失败: {message}"
    
    # 获取用户信息
    user = user_manager.get_user('test_beijing_user')
    assert user is not None, "用户不存在"
    
    # 验证创建时间
    created_at = user['created_at']
    print(f"用户创建时间: {created_at}")
    assert "+08:00" in created_at, f"创建时间应包含北京时区信息: {created_at}"
    
    # 测试登录时间更新
    login_success, login_message = user_manager.login_user('test_beijing_user', 'test123456')
    assert login_success, f"登录失败: {login_message}"
    
    # 验证最后登录时间
    user = user_manager.get_user('test_beijing_user')
    last_login = user['last_login']
    print(f"最后登录时间: {last_login}")
    assert "+08:00" in last_login, f"最后登录时间应包含北京时区信息: {last_login}"
    
    # 测试生成记录
    user_manager.add_generation_record('test_beijing_user', 'image', '测试提示词')
    user = user_manager.get_user('test_beijing_user')
    
    if user['generation_history']:
        gen_time = user['generation_history'][0]['timestamp']
        print(f"生成记录时间: {gen_time}")
        assert "+08:00" in gen_time, f"生成记录时间应包含北京时区信息: {gen_time}"
    
    # 清理测试文件
    if os.path.exists('test_users.json'):
        os.remove('test_users.json')
    
    print("✓ 用户管理系统时间处理测试通过\n")

def test_redemption_system_time():
    """测试兑换系统的时间处理"""
    print("=== 测试兑换系统时间处理 ===")
    
    # 创建测试兑换系统
    redemption_system = RedemptionSystem('test_redemption_codes.json')
    
    # 创建测试兑换码
    codes = redemption_system.create_redemption_codes(
        code_type='one_time',
        points=10,
        count=1,
        expire_days=7,
        description='测试北京时间'
    )
    
    assert len(codes) == 1, "应该创建1个兑换码"
    
    # 获取兑换码信息
    code_info = redemption_system.get_code_info(codes[0])
    assert code_info is not None, "兑换码信息不存在"
    
    # 验证创建时间
    created_at = code_info['created_at']
    print(f"兑换码创建时间: {created_at}")
    assert "+08:00" in created_at, f"创建时间应包含北京时区信息: {created_at}"
    
    # 验证过期时间
    expire_at = code_info['expire_at']
    print(f"兑换码过期时间: {expire_at}")
    assert "+08:00" in expire_at, f"过期时间应包含北京时区信息: {expire_at}"
    
    # 测试使用兑换码
    success, message, points = redemption_system.use_redemption_code(codes[0], 'test_user')
    assert success, f"使用兑换码失败: {message}"
    
    # 验证使用记录时间
    usage_records = redemption_system.get_user_usage_records('test_user')
    if usage_records:
        used_at = usage_records[0]['used_at']
        print(f"兑换码使用时间: {used_at}")
        assert "+08:00" in used_at, f"使用时间应包含北京时区信息: {used_at}"
    
    # 清理测试文件
    if os.path.exists('test_redemption_codes.json'):
        os.remove('test_redemption_codes.json')
    
    print("✓ 兑换系统时间处理测试通过\n")

def test_time_consistency():
    """测试各系统时间的一致性"""
    print("=== 测试系统时间一致性 ===")
    
    # 获取各系统的当前时间
    beijing_time = beijing_now()
    
    # 创建临时系统实例
    points_system = PointsSystem('temp_points.json')
    user_manager = UserManager('temp_users.json')
    redemption_system = RedemptionSystem('temp_redemption.json')
    
    # 记录操作时间
    start_time = beijing_now()
    
    # 执行各种操作
    points_system.add_transaction('test', 10, 'test', 'consistency test')
    user_manager.register_user('test_consistency', 'password123')
    codes = redemption_system.create_redemption_codes('one_time', 5, 1, 1, 'consistency test')
    
    end_time = beijing_now()
    
    # 验证时间范围
    time_diff = (end_time - start_time).total_seconds()
    print(f"操作耗时: {time_diff:.2f} 秒")
    assert time_diff < 5, "操作时间过长，可能存在问题"
    
    # 清理临时文件
    for temp_file in ['temp_points.json', 'temp_users.json', 'temp_redemption.json']:
        if os.path.exists(temp_file):
            os.remove(temp_file)
    
    print("✓ 系统时间一致性测试通过\n")

def main():
    """主测试函数"""
    print("开始北京时间集成测试...\n")
    
    try:
        test_beijing_time_module()
        test_points_system_time()
        test_auth_system_time()
        test_redemption_system_time()
        test_time_consistency()
        
        print("🎉 所有测试通过！北京时间转换成功完成。")
        print(f"当前北京时间: {format_beijing_time()}")
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
